# KPI Aggregation Service

## 项目概述

KPI Aggregation Service 是一个用于收集、处理和分析系统性能指标（KPI）的分布式数据聚合服务。该服务通过TCP接收来自各个业务系统的KPI数据，进行实时聚合处理，并将结果输出到Kafka消息队列和Doris数据库中。

## 主要功能

- **实时数据接收**: 通过TCP Socket接收JSON格式的KPI数据
- **消息解析与转换**: 支持多种消息格式的解析和标准化处理
- **智能聚合**: 根据配置规则对KPI数据进行时间窗口聚合
- **多目标输出**: 支持Kafka消息队列和Doris数据库双路输出
- **负载均衡**: 多进程架构实现负载均衡和故障转移
- **动态配置**: 支持运行时配置更新（部分参数）

## 系统架构

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    KPI Aggregation Service                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐    ┌───────────┐ │
│  │   Main Process  │    │  Child Process  │    │   Doris   │ │
│  │                 │    │                 │    │  Database │ │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │           │ │
│  │ │   Socket    │ │───▶│ │   Parser    │ │    │           │ │
│  │ │   Listener  │ │    │ │   Engine    │ │    │           │ │
│  │ └─────────────┘ │    │ └─────────────┘ │    │           │ │
│  │                 │    │                 │    │           │ │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │           │ │
│  │ │ Process     │ │    │ │  Output     │ │    │           │ │
│  │ │ Manager     │ │    │ │  Handler    │ │    │           │ │
│  │ └─────────────┘ │    │ └─────────────┘ │    │           │ │
│  └─────────────────┘    └─────────────────┘    └───────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 进程架构

- **主进程**: 负责监听端口、管理子进程、负载均衡
- **子进程**: 负责消息处理、数据聚合、结果输出
- **多线程**: 每个子进程内部使用多线程处理不同阶段的数据

## 技术栈

- **编程语言**: Python 3.9.7
- **消息中间件**: Apache Kafka
- **数据库**: Apache Doris (实时分析型数据库)
- **配置管理**: XML配置文件
- **进程通信**: 多进程 + 管道通信
- **日志系统**: 自定义日志模块

## 项目结构

```
kpiAggregation/
├── cfg/                           # 配置文件目录
│   ├── kpiAggregation.cfg.xml    # 主配置文件
│   ├── kpiAggregation.sql.xml   # 数据库配置
│   └── kpi建表脚本.sql          # 数据库表结构
├── src/                          # 源代码目录
│   ├── kpiAggregation.py        # 主程序入口
│   ├── DCKpiAggServMan.py       # 主进程管理器
│   ├── DCKpiConMgr.py           # 子进程连接管理器
│   ├── DCKpiMesParse.py         # 消息解析器
│   ├── DCKpiMesTrans.py         # 消息转换器
│   ├── DCKpiMesOut.py           # 消息输出器
│   ├── DCKpiDbLoad.py           # 数据库加载器
│   ├── DCKpiStatistics.py       # 统计模块
│   ├── DCConfig.py              # 配置管理器
│   ├── DBPyMgr.py               # 数据库连接管理
│   ├── desTool.py               # 加密工具
│   └── logging2.py              # 日志模块
└── make.sh                      # 构建脚本
```

## 模块详解

### 1. 主程序入口 (kpiAggregation.py)

- **功能**: 服务启动入口，负责初始化配置和启动主进程
- **信号处理**: 支持SIGTERM、SIGQUIT、SIGINT优雅退出
- **配置热更新**: 支持运行时日志级别更新

### 2. 主进程管理器 (DCKpiAggServMan.py)

- **进程管理**: 创建和管理多个子进程
- **负载均衡**: 使用轮询算法分配连接请求
- **故障恢复**: 子进程故障自动重启，防止频繁重启
- **端口监听**: 支持多端口并发监听

### 3. 子进程连接管理器 (DCKpiConMgr.py)

- **连接处理**: 处理来自主进程的客户端连接
- **线程池管理**: 管理消息解析、转换、输出线程
- **队列管理**: 维护任务队列和输出队列
- **资源清理**: 优雅关闭所有资源

### 4. 消息处理组件

- **DCKpiMesParse**: JSON消息解析和验证
- **DCKpiMesTrans**: 消息格式转换和聚合处理
- **DCKpiMesOut**: Kafka消息队列输出
- **DCKpiDbLoad**: Doris数据库批量加载

## 配置说明

### 主配置文件 (kpiAggregation.cfg.xml)

#### 日志配置
```xml
<log>
    <param name="logpath">/path/to/logs</param>
    <param name="loglevel">DEBUG</param>
</log>
```

#### 通用配置
```xml
<common>
    <param name="workPidNums">1</param>          <!-- 子进程数量 -->
    <param name="deal_thread_num">1</param>      <!-- 消息解析线程数 -->
    <param name="out_thread_num">4</param>       <!-- Kafka输出线程数 -->
    <param name="queue_size">1000</param>        <!-- 队列大小 -->
</common>
```

#### Kafka配置
```xml
<kafka>
    <param name="bootstrap_servers">**************:9092</param>
    <param name="security_protocol">SASL_PLAINTEXT</param>
    <param name="sasl_mechanism">PLAIN</param>
    <param name="sasl_plain_username">itete</param>
    <param name="sasl_plain_password">***</param>
</kafka>
```

#### 消息规则配置
```xml
<mes_rule>
    <group1>
        <param name='rule_name'>default</param>
        <param name='mes_input'>JSON格式模板</param>
        <param name='merge_key'>合并键</param>
        <param name='kafka_topic'>输出topic</param>
        <param name='merge_time'>30</param>      <!-- 聚合时间窗口 -->
        <param name='merge_latency'>10</param>   <!-- 聚合延迟时间 -->
    </group1>
</mes_rule>
```

### 数据库配置 (kpiAggregation.sql.xml)

```xml
<db_set>
    <db name='mysqlCon' type='mysql'>
        <connect>
            <param name="host">**************</param>
            <param name="port">3308</param>
            <param name="user">root</param>
            <param name="password">***</param>
            <param name="db">bp-ticket</param>
        </connect>
    </db>
</db_set>
```

## 数据模型

### KPI定义表 (ops_sys_kpi_detail_def)
- 存储KPI指标的定义信息
- 关联业务模块和指标编码
- 支持cycle和state两种指标类型

### KPI详情表 (ops_sys_kpi_detail)
- Doris聚合表，支持实时分析
- 30个KpiCycle字段用于数值指标
- 7个KpiState字段用于状态指标
- 按天分区，支持高效时间范围查询

## 部署指南

### 环境要求
- Python 3.9.7+
- Apache Kafka集群
- Apache Doris数据库
- MySQL配置数据库

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd kpiAggregation
```

2. **安装依赖**
```bash
conda activate python3.9.7
pip install -r requirements.txt
```

3. **配置修改**
- 修改 `cfg/kpiAggregation.cfg.xml` 中的Kafka、数据库连接信息
- 根据业务需求配置消息规则和聚合参数

4. **数据库初始化**
```bash
# 执行建表脚本
mysql -h<host> -P<port> -u<user> -p<password> < cfg/kpi建表脚本.sql
```

5. **服务启动**
```bash
# 启动服务
python src/kpiAggregation.py cfg/kpiAggregation.cfg.xml

# 或使用构建版本
./kpiAggregation cfg/kpiAggregation.cfg.xml
```

6. **构建可执行文件**
```bash
# 使用提供的构建脚本
./make.sh

# 或手动构建
cd src
pyinstaller -F kpiAggregation.py
```

## 使用示例

### 发送测试数据

```python
import socket
import json

def send_test_data():
    data = {
        "batid": "20240101",
        "cluster": "prod",
        "data": [{
            "Host": "*************",
            "TraceKpi": {"cpu_usage": 85, "memory_usage": 70},
            "pid": "12345",
            "sub_cluster": "node1",
            "type": "performance"
        }],
        "module": "billing",
        "subsys": "core",
        "system": "LN_5G"
    }
    
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect(('**************', 8686))
    sock.send(json.dumps(data).encode())
    sock.close()
```

### 查询聚合结果

```sql
-- 查询最近一小时的指标数据
SELECT 
    system, subsys, module, Host,
    KpiCycle1, KpiCycle2, KpiCycle3,
    createDate
FROM ops_sys_kpi_detail
WHERE createDate >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY createDate DESC;
```

## 监控与运维

### 日志监控
- 日志路径: `/codebill/LN/src_5g/tools/kpiAggregation/log/`
- 日志级别: DEBUG, INFO, WARNING, ERROR
- 关键日志关键词: "KPI Aggregation Service start", "child pid", "Accepted connection"

### 性能指标
- **接收速率**: 每秒处理的消息数量
- **聚合延迟**: 消息处理到输出的时间延迟
- **队列长度**: 内部队列的积压情况
- **进程状态**: 子进程的健康状态

### 故障排查

1. **服务无法启动**
   - 检查端口是否被占用
   - 验证配置文件格式
   - 确认Kafka和数据库连接

2. **数据丢失**
   - 检查Kafka topic是否存在
   - 验证Doris表结构
   - 查看日志中的错误信息

3. **性能问题**
   - 调整子进程数量 (`workPidNums`)
   - 优化线程池配置
   - 检查系统资源使用情况

## 扩展指南

### 添加新的消息格式

1. 在 `kpiAggregation.cfg.xml` 中添加新的消息规则组
2. 定义消息输入格式和聚合键
3. 配置对应的Kafka topic
4. 更新KPI定义表

### 自定义聚合逻辑

1. 修改 `DCKpiMesTrans.py` 中的聚合算法
2. 调整时间窗口和延迟参数
3. 扩展KPI详情表的字段定义

## 版本历史

- **v1.0.0**: 初始版本，支持基本KPI聚合功能
- **v1.1.0**: 增加多进程架构和负载均衡
- **v1.2.0**: 支持动态配置更新和故障恢复
- **v1.3.0**: 增加Doris数据库输出和统计功能

## 许可证

本项目采用私有许可证，仅限辽宁移动内部使用。

## 技术支持

如有问题，请联系开发团队：
- 邮箱: <EMAIL>
- 电话: 024-12345678