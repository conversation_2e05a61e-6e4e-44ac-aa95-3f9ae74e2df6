#!/usr/bin/python
# -*- coding: utf-8 -*-
#统计输出

import threading
import logging2
import time
import traceback
from collections import defaultdict

class DCKpiStatistics(threading.Thread):
    def __init__(self, isRun):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_oldTime = time.time()
        self.m_stats = defaultdict(lambda: {'total': 0, 'old_total': 0})
        self.m_lock = threading.Lock()
        pass

    def setRunState(self, isRun):
        self.m_isRun = isRun


    def kpi_statistics(self, module):
        try:
            # 模拟接收消息
            with self.m_lock:
                self.m_stats[module]['total'] += 1
        except Exception as err:
            logging2.error("kpi_statistics error..")
            traceback.print_exc()
            logging2.error(str(err))

    def print_stats(self):
        try:
            stats_copy = {}
            with self.m_lock:
                for name, s in self.m_stats.items():
                    stats_copy[name] = {'total': s['total'], 'old_total': s['old_total']}
            
            for name, s in stats_copy.items():
                total_received = s['total']
                last_min_received = s['total'] - s['old_total']
                tps = last_min_received / 60.0  # 计算TPS
                logging2.warn(f"{name} Total: {total_received}, 1min_received: {last_min_received}, TPS: {tps:.2f}")
            
            with self.m_lock:
                for name, s in self.m_stats.items():
                   s['old_total'] = stats_copy[name]['total']
        except Exception as err:
            logging2.error("print_stats error..")
            traceback.print_exc()
            logging2.error(str(err))

    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiStatistics thread exit")
                break
            try:
                now_time = time.time()
                if now_time - self.m_oldTime >= 60:
                    self.print_stats()
                    self.m_oldTime = now_time
                time.sleep(10)
            except Exception as err:
                logging2.error("DCKpiStatistics run error..")
                traceback.print_exc()
                logging2.error(str(err))