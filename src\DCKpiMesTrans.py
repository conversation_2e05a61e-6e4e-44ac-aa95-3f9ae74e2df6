#!/usr/bin/python
# -*- coding: utf-8 -*-
#指标消息的合并以及转换

import queue
import threading
import logging2
import time
import traceback

class DCKpiMesTrans(threading.Thread):
    def __init__( self, isRun, cfgObj, outQueue ):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_outQueue = outQueue
        #保持数据一致性的锁
        self.m_lock = threading.Lock()
        #key为合并维度 value为数据格式 key：value格式
        self.m_dataDict = {}
        #key为时间value为list list中串的是m_dataDict中的key
        self.m_timeDict = {}

        pass

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def addDataCycle(self, dataKey, key, value, timeDimen):
        try:
            with self.m_lock:
                #第一次加入设置系统时间戳
                if dataKey not in self.m_dataDict:
                    self.m_dataDict[dataKey] = {"now_time": time.time()}
                    if timeDimen not in self.m_timeDict:
                        self.m_timeDict[timeDimen] = []                
                    self.m_timeDict[timeDimen].append(dataKey)
                    #获取元素位置，方便删除
                    self.m_dataDict[dataKey]["key_pos"] = len(self.m_timeDict[timeDimen]) - 1
                
                if key in self.m_dataDict[dataKey] and isinstance(self.m_dataDict[dataKey][key], (int, float)):
                    self.m_dataDict[dataKey][key] += int(float(value))
                else:
                    self.m_dataDict[dataKey][key] = int(float(value))                   
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))  

    def addDataState(self, dataKey, key, value, timeDimen, batchId=None):
        try:
            with self.m_lock:
                #第一次加入设置系统时间戳
                if dataKey not in self.m_dataDict:
                    self.m_dataDict[dataKey] = {"now_time": time.time()}
                    if timeDimen not in self.m_timeDict:
                        self.m_timeDict[timeDimen] = []                
                    self.m_timeDict[timeDimen].append(dataKey)
                    #获取元素位置，方便删除
                    self.m_dataDict[dataKey]["key_pos"] = len(self.m_timeDict[timeDimen]) - 1
                
                if key in self.m_dataDict[dataKey]: 
                    if self.m_dataDict[dataKey].get("batchId", None) is not None and batchId is not None and self.m_dataDict[dataKey]['batchId'] > batchId:
                        pass
                    else:
                        self.m_dataDict[dataKey][key] = value
                    # if isinstance(self.m_dataDict[dataKey][key], (int, float, str)) and isinstance(value, (int, float, str)) and self.m_dataDict[dataKey][key] < value:
                    #     self.m_dataDict[dataKey][key] = value 
                    # else:
                    #     self.m_dataDict[dataKey][key] = value     
                else:
                    self.m_dataDict[dataKey][key] = value  
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
    
    def addDataStateAvg(self, dataKey, key, value, timeDimen):
        try:
            with self.m_lock:
                #第一次加入设置系统时间戳
                if dataKey not in self.m_dataDict:
                    self.m_dataDict[dataKey] = {"now_time": time.time()}
                    if timeDimen not in self.m_timeDict:
                        self.m_timeDict[timeDimen] = []                
                    self.m_timeDict[timeDimen].append(dataKey)
                    #获取元素位置，方便删除
                    self.m_dataDict[dataKey]["key_pos"] = len(self.m_timeDict[timeDimen]) - 1
                
                if key in self.m_dataDict[dataKey]: 
                    if isinstance(self.m_dataDict[dataKey][key], (int, float, str)) and isinstance(value, (int, float, str)):
                        try:
                            # 尝试将值转换为浮点数
                            num1 = float(self.m_dataDict[dataKey][key])
                            num2 = float(value)
                            total = num1 + num2
                            self.m_dataDict[dataKey][key] = total#round(average, 2)
                            keyCnt = key + "_cnt"                            
                            self.m_dataDict[dataKey][keyCnt] += 1
                        except ValueError:
                            # 如果转换失败，说明至少有一个值不是有效的数字，直接赋值
                            self.m_dataDict[dataKey][key] = value
                    else:
                        self.m_dataDict[dataKey][key] = value     
                else:
                    self.m_dataDict[dataKey][key] = value 
                    keyCnt = key + "_cnt"
                    self.m_dataDict[dataKey][keyCnt] = 1
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))

    def addData(self, dataKey, dataDict, timeDimen):
        try:
            with self.m_lock:
                if dataKey in self.m_dataDict:
                    mergeData = {**self.m_dataDict[dataKey], **dataDict}
                    self.m_dataDict[dataKey] = mergeData
                    logging2.debug("the dataKey[%s] already exist! mergeData"%(dataKey))
                    return
                if timeDimen not in self.m_timeDict:
                    self.m_timeDict[timeDimen] = []                
                self.m_timeDict[timeDimen].append(dataKey)
                #获取元素位置，方便删除
                key_pos = len(self.m_timeDict[timeDimen]) - 1
                dataDict["key_pos"] = key_pos
                dataDict["now_time"] = time.time()
                self.m_dataDict[dataKey] = dataDict                            
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))

    def delData(self, dataKey, timeDimen):
        try:
            with self.m_lock:
                if dataKey not in self.m_dataDict:
                    logging2.error("the dataKey[%s] not exist!"%(dataKey))
                    return
                key_pos = int(self.m_dataDict[dataKey]["key_pos"])
                del self.m_dataDict[dataKey]           
                if timeDimen not in self.m_timeDict:
                    logging2.error("the timeDimen[%s] not exist!"%(timeDimen))   
                    return             
                self.m_timeDict[timeDimen].pop(key_pos)                
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))

    def scanData(self):
        try:
            with self.m_lock:
                if not self.m_timeDict:
                    return
                nowTime = time.time()
                # 创建一个键的副本以避免在迭代时修改字典
                timeDimen_keys = list(self.m_timeDict.keys())
                for timeDimen in timeDimen_keys:    
                    if int(timeDimen) <= 0:    
                        while self.m_timeDict[timeDimen]:
                            dataKey = self.m_timeDict[timeDimen].pop(0)
                            try:
                                self.m_outQueue.put(self.m_dataDict[dataKey], block=False)
                            except queue.Full:
                                logging2.warn(f"Queue full, discard the key: {dataKey}")
                            del self.m_dataDict[dataKey]
                        del self.m_timeDict[timeDimen]
                    else:       
                        i = 0     
                        while i < len(self.m_timeDict[timeDimen]):
                            dataKey = self.m_timeDict[timeDimen][i]
                            if nowTime - self.m_dataDict[dataKey]["now_time"] > int(timeDimen):
                                try:
                                    self.m_outQueue.put(self.m_dataDict[dataKey], block=False)
                                except queue.Full:
                                    logging2.warn(f"Queue full, discard the key: {dataKey}")   
                                del self.m_dataDict[dataKey] 
                                self.m_timeDict[timeDimen].pop(i) 
                            else:
                                break                         
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
    
    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiMesTrans thread exit")
                break
            try:
                self.scanData()
                time.sleep(1)  # 休眠5秒钟，可改小              
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))