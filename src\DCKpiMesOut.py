#!/usr/bin/python
# -*- coding: utf-8 -*-
#指标消息写kafka

import queue
import threading
import logging2
import time,sys
import traceback
import json
from kafka import KafkaProducer
import datetime

class DCKpiMesOut(threading.Thread):
    def __init__(self, isRun, cfgObj, outQueue, dbLoad, statistics ):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_outQueue = outQueue
        self.m_dbLoad = dbLoad          
        self.m_kafkaCfgObj = self.m_cfgObj.get_group_value('kafka')[0]
        self.m_separator = '^'

        self.m_KpiCycleName = 'KpiCycle'
        self.m_KpiCycleNums = 30
        self.m_KpiStateName = 'KpiState'
        self.m_KpiStateNums = 7
        self.m_KpiComName = 'common'
        self.m_statistics = statistics
        self.m_moduleName = "OUT"
        if not self.initField():
            logging2.error(f"initField failed")
            raise Exception("initField failed")
        self.m_outMsg = ""
        self.m_topic = ""
        if not self.createKafkaObj():
            logging2.error(f"kafka init failed")
            raise Exception("kafka init failed")
        pass

    def initField(self):
        try:
            value = str(self.m_cfgObj.get_single_value('common', 'separator'))
            if value is None or value == '':
                self.m_separator = '^'
            else:
                self.m_separator = value
            logging2.debug(f"m_separator:{self.m_separator}")
            value = self.m_cfgObj.get_single_value('common', 'cycleNums')
            if value is not None:
                #格式 "KpiCycle|数字"
                parts = value.split('|')
                if len(parts) == 2:
                    self.m_KpiCycleName = parts[0]
                    # 确保第二部分是数字
                    if parts[1].isdigit():
                        self.m_KpiCycleNums = int(parts[1])  # 将字符串转换为整数
                        # 这里你可以根据需要使用 kpi_cycle_value
                    else:
                        logging2.error("格式错误，'KpiCycle'后面的部分应该是一个数字。")
                else:
                    logging2.error("格式错误，期望的格式是 'KpiCycle|数字'。")
            logging2.debug(f"{self.m_KpiCycleName}|{self.m_KpiCycleNums}")
            value = self.m_cfgObj.get_single_value('common', 'stateNums')
            if value is not None:
                #格式 "KpiState|数字"
                parts = value.split('|')
                if len(parts) == 2:
                    self.m_KpiStateName = parts[0]
                    # 确保第二部分是数字
                    if parts[1].isdigit():
                        self.m_KpiStateNums = int(parts[1])  # 将字符串转换为整数
                        # 这里你可以根据需要使用 kpi_cycle_value
                    else:
                        logging2.error("格式错误，'KpiState'后面的部分应该是一个数字。")
                else:
                    logging2.error("格式错误，期望的格式是 'KpiState|数字'。")
            logging2.debug(f"{self.m_KpiStateName}|{self.m_KpiStateNums}")
            value = self.m_cfgObj.get_single_value('common', 'comModule')
            if value is not None:
                self.m_KpiComName = value
            logging2.debug(f"{self.m_KpiComName}")
            return True
        except Exception as err:
            traceback.print_exc()
            logging2.error(str(err))
            return False

    def setRunState(self, isRun):
        self.m_isRun = isRun

    def createProducer(self):
        producer = None
        try:
            if self.m_kafkaCfgObj.get('safe_cert') is None:
                logging2.error(f"safe_cert is None")
                return producer

            if self.m_kafkaCfgObj['safe_cert'] == '1':
                producer = KafkaProducer(
                    bootstrap_servers=self.m_kafkaCfgObj['bootstrap_servers'],
                    value_serializer=lambda v: v.encode('utf-8'),
                    security_protocol=self.m_kafkaCfgObj['security_protocol'],  # 添加安全协议
                    sasl_mechanism=self.m_kafkaCfgObj['sasl_mechanism'],  # 添加SASL机制
                    sasl_plain_username=self.m_kafkaCfgObj['sasl_plain_username'],  # 添加用户名
                    sasl_plain_password=self.m_kafkaCfgObj['sasl_plain_password']  # 添加密码
                )
            else:
                # producer = KafkaProducer(
                #     bootstrap_servers=self.m_kafkaCfgObj['bootstrap_servers'],
                #     value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8')
                # )
                producer = KafkaProducer(
                    bootstrap_servers=self.m_kafkaCfgObj['bootstrap_servers'],
                    value_serializer=lambda v: v.encode('utf-8')
                )

            return producer
        except Exception as e:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            logging2.error(f"{str(e)}")
            return producer

    def createKafkaObj(self):
        self.m_producer = None
        try:
            self.m_producer = self.createProducer()
            try:
                metrics = self.m_producer.metrics()
                logging2.info("生产者连接成功,metrics信息:%s:"%(metrics))
            except AttributeError:
                logging2.error("生产者连接可能失败:%s,无法获取metrics"%(self.m_kafkaCfgObj.get('bootstrap_servers', None)))
                return False
            logging2.info(f"kafka [{self.m_kafkaCfgObj.get('bootstrap_servers', None)}] init success")
        except Exception as e:
            logging2.error("kafka:%s init failed:%s"%(self.m_kafkaCfgObj.get('bootstrap_servers', None), e))
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            return False
        return True


    def checkKafkaConn(self):
        try:
            producer = self.createProducer()
            try:
                metrics = producer.metrics()
            except AttributeError:
                logging2.error(f"生产者连接可能失败[{self.m_kafkaCfgObj['bootstrap_servers']}],无法获取metrics")
                return False
            producer.close()
            return True
        except Exception as e:
            logging2.error(f"kafka [{self.m_kafkaCfgObj['bootstrap_servers']}] connect failed, {e}")
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            return False      


    def releaseKafkaObj(self):
        try:
            self.m_producer.close()
            logging2.info(f'kafka release success')
        except Exception as e:
            logging2.error(f'kafka release failed: {e}')

    # 生产消息
    def produceMsg(self):
        # 发送消息
        if self.checkKafkaConn() == False:
            logging2.error(f'produceMsg error')
            return False
        try:
            self.m_producer.send(self.m_topic, self.m_outMsg)
            self.m_producer.flush()
            logging2.info(f'produceMsg success! topic:%s, msg:%s'%(self.m_topic, self.m_outMsg))
            self.m_statistics.kpi_statistics(self.m_moduleName)
        except Exception as e:
            logging2.error(f'produceMsg error: %s, topic:%s, message:%s'%(e, self.m_topic, self.m_outMsg))
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            return False
        return True
    
    def combineMsg(self, data):
        try:
            #加载数据库获取字段明；2.根据给定的表结构拼接字段用^分割，只输出已存在数据库中的字段
            commonList = self.m_dbLoad.getCommonData()
            moduleDict = self.m_dbLoad.getModuleData(data['module'].lower())
            if not commonList or not moduleDict:
                logging2.error(f"commonDict or moduleDict is None, commonDict:{commonList}, moduleDict:{moduleDict}")
                return False
            dataList = []
            common_dict = {}
            for key in commonList:
                if key in data:
                    dataList.append(data[key])
                else:
                    if key == 'latnid':#做latnId到sub_cluster的映射
                        if 'sub_cluster' in data:
                            dataList.append(data['sub_cluster'])
                    else:
                        logging2.error(f"key:{key} not exist in msg:{data}")
                        dataList.append('')
                common_dict[key] = 0

            batid = data.get('batid', None)
            month = datetime.datetime.now().month
            if batid:
                try:
                    # 按照 20250226113900 格式解析日期并提取天
                    month = datetime.datetime.strptime(str(batid), '%Y%m%d%H%M%S').month
                except ValueError:
                    logging2.error(f"batid:{batid} 格式错误，无法解析日期，使用系统时间")
                    month = datetime.datetime.now().month
            dataList.append(int(month))#在common后面增加batchDay
            commonLen = len(dataList)

            dataList.extend([0] * self.m_KpiCycleNums)
            dataList.extend([''] * self.m_KpiStateNums)

            cycleNameLen = len(self.m_KpiCycleName)
            stateNameLen = len(self.m_KpiStateName)
            for key,value in data.items():
                if key in moduleDict:
                    logging2.debug(f"key:%s, value:%s"%(key, value))
                    if moduleDict[key][1] == 1:#cycyle
                        cycleIdxStr = moduleDict[key][0][cycleNameLen:]
                        if not cycleIdxStr.isdigit() or int(cycleIdxStr) > self.m_KpiCycleNums:
                            logging2.error(f"{moduleDict[key][0]} format error, should be {self.m_KpiCycleName}+number")
                            return False
                        cycleIdx = int(cycleIdxStr) - 1
                        dataList[cycleIdx+commonLen] = value
                    else:
                        stateIdxStr = moduleDict[key][0][stateNameLen:]
                        if not stateIdxStr.isdigit() or int(stateIdxStr) > self.m_KpiStateNums:
                            logging2.error(f"{moduleDict[key][0]} format error, should be {self.m_KpiStateName}+number")
                            return False
                        stateIdx = int(stateIdxStr) - 1
                        if moduleDict[key][1] == 3:#avg state
                            cnt_key = key + '_cnt'
                            cnt = data.get(cnt_key, 1)
                            value = float(value) / cnt
                            dataList[stateIdx+self.m_KpiCycleNums+commonLen] = round(value, 2)
                        else:
                            dataList[stateIdx+self.m_KpiCycleNums+commonLen] = value
                else:
                    if key not in common_dict and key != 'sub_cluster':
                        logging2.info(f"field[{key}] not exist in ops_sys_kpi_detail_def moduleName[{data['module']}]")

            logging2.info(f"separator:{self.m_separator} dataList:{len(dataList)}")
            self.m_outMsg = self.m_separator.join(map(str, dataList))
            logging2.info(f'combineMsg message:%s' % (self.m_outMsg))

            # 获取topic
            cfgModule = self.m_cfgObj.get_group_value('mes_rule', data['module'])
            if not cfgModule:
                logging2.error("the module[%s] not exist in config file!"%(data['module']))
                # 判断是否在指标表里有，如果有则加载默认规则，数据加载都转成了小写字母
                moduleDef = self.m_dbLoad.getModuleData(data['module'].lower())
                if moduleDef == None:
                    logging2.error("the module[%s] not exist in db!"%(data['module']))
                    return False
                else:
                    logging2.info("the module[%s] not exist in config file, but exist in db, use default rule!"%(data['module']))
                    cfgModule = self.m_cfgObj.get_group_value('mes_rule', 'default')
                    if not cfgModule:
                        logging2.error("the default module not exist in config file!")
                        return False

            self.m_topic = cfgModule['kafka_topic']
            return True
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            logging2.error(str(err))
            traceback.print_exc()
            return False

    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiMesOut thread exit")
                self.releaseKafkaObj()
                break
            try:
                #设置为非阻塞模式
                if self.m_outQueue.empty():
                    time.sleep(0.5)
                    continue

                data = self.m_outQueue.get(timeout=1)
                if data is None:
                    time.sleep(0.5)
                    continue
                logging2.debug("get data:%s"%(data))
                #按照模板解析数据
                if self.combineMsg(data):
                    self.produceMsg()
                else:
                    logging2.error("combineMsg error, data:%s"%(data))

            except queue.Empty:
                time.sleep(0.5)  
            except Exception as err:
                self.releaseKafkaObj()
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))