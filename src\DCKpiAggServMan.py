#!/usr/bin/python
# -*- coding: utf-8 -*-
#主进程连接管理
import multiprocessing
import logging2
import socket
import time
import traceback
import threading
from DCKpiConMgr import DCKpiConMgr  
import sys
import select

class ThreadSafeCounter:
    def __init__(self, initial_value=0):
        self.value = initial_value
        self._lock = threading.Lock()
        self.max_value = sys.maxsize  # int64的最大值

    def increment(self):
        with self._lock:
            if self.value >= self.max_value:
                self.value = 0
            else:
                self.value += 1
            return self.value

    def decrement(self):
        with self._lock:
            if self.value > 0:
                self.value -= 1
            return self.value

    def get_value(self):
        with self._lock:
            return self.value

    def reset(self):
        with self._lock:
            self.value = 0

class DCKpiAggService(threading.Thread):
    def __init__(self, isRun, serverCfg, counter, pidNums, parentPipeList ):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_serverCfg = serverCfg
        self.m_counter = counter
        self.m_pidNums = pidNums
        self.m_parentPipeList = parentPipeList
        self.m_socket = None
        pass

    def bindSocket(self):
        # with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as self.m_socket:
        self.m_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            # 绑定IP地址和端口号
            self.m_socket.bind(( self.m_serverCfg['server_ip'], int(self.m_serverCfg['server_port']) ))
            # 设置最大连接数为10
            self.m_socket.listen(10)
            logging2.info("ip:port=(%s:%s)"%(self.m_serverCfg['server_ip'], self.m_serverCfg['server_port']))
            # 设置套接字为非阻塞模式
            self.m_socket.setblocking(0)
            return 0
        except socket.error as err:
            # 捕获到socket错误，特别是绑定端口失败
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            return -1
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return -1
    def setRunState(self, isRun):
        self.m_isRun = isRun

    def run(self):
        # 创建socket对象
        # with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            try:
                # # 绑定IP地址和端口号
                # sock.bind(( self.m_serverCfg['server_ip'], int(self.m_serverCfg['server_port']) ))
                # # 设置最大连接数为10
                # sock.listen(10)
                # logging2.info("ip:port=(%s:%s)"%(self.m_serverCfg['server_ip'], self.m_serverCfg['server_port']))
                # # 设置套接字为非阻塞模式
                # sock.setblocking(0)
                if self.m_socket.fileno() == -1:
                    raise ValueError("Socket file descriptor is invalid.")
                while  self.m_isRun: 
                    readable, _, _ = select.select([self.m_socket], [], [], 0.5)  # 0.5秒超时
                    if readable:
                        client_socket, addr = self.m_socket.accept()               
                        logging2.warn("Accepted connection from %s:%s"%(addr[0], addr[1]))
                        # 通过管道将客户端套接字传递给子进程
                        parentPipeIdx = int(self.m_counter.increment())%self.m_pidNums
                        self.m_parentPipeList[parentPipeIdx].send((client_socket, addr))
                        logging2.warn("send connetct ok! parentPipeIdx=%d"%(parentPipeIdx))
                logging2.warn("DCKpiAggService thread exit!")
            except socket.error as err:
                # 捕获到socket错误，特别是绑定端口失败
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))

class DCKpiAggServMan(threading.Thread):
    def __init__(self, isRun, cfgObj, cfgFile):
        threading.Thread.__init__(self)
        self.m_isRun = isRun
        self.m_cfgObj = cfgObj
        self.m_cfgPidNums = int(self.m_cfgObj.get_single_value('common', 'workPidNums'))
        self.m_parentPipe = [None] * self.m_cfgPidNums
        self.m_childConn = [None] * self.m_cfgPidNums
        self.m_childPid = [None] * self.m_cfgPidNums
        self.m_kpiConMgrObj = [None] * self.m_cfgPidNums
        self.m_counter = ThreadSafeCounter()
        self.m_listenThreads = []
        self.m_cfgFile = cfgFile
        self.restart_counts = [0] * self.m_cfgPidNums
        self.last_restart_time = [0] * self.m_cfgPidNums

        #创建子进程
        if self.main_process() < 0:
            self.exitDeal()
            logging2.error("main_process failed!")
            raise Exception("main_process failed!")
        #监听端口
        if self.listenPort() < 0:
            self.exitDeal()
            logging2.error("listenPort failed!")
            raise Exception("listenPort failed!")
        #检测子进程的状态
        pass
        
    def setRunState(self, isRun):
        self.m_isRun = isRun
        self.exitDeal()
    
    def getRunState(self):
        return self.m_isRun

    def main_process(self):
        try:
            # 创建管道
            for i in range( 0, self.m_cfgPidNums ):
                # 创建父进程和子进程之间的管道
                self.m_parentPipe[i], self.m_childConn[i] = multiprocessing.Pipe()
                # 创建子进程
                self.m_kpiConMgrObj[i] = DCKpiConMgr(self.m_cfgFile)
                # self.m_childPid[i] = multiprocessing.Process(target=self.m_kpiConMgrObj[i].child_process, args=(self.m_childConn[i],))
                self.m_childPid[i] = multiprocessing.Process(target=self.m_kpiConMgrObj[i].init_child_process, args=(self.m_childConn[i],))
                self.m_childPid[i].start()
                self.last_restart_time[i] = time.time()

                logging2.warn(f"child pid[%s] create sucess!"%(self.m_childPid[i].pid))
            return 0    
        except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                traceback.print_exc()
                logging2.error(str(err))
                return -1

    def listenPort(self):
        try:
            serverList = self.m_cfgObj.get_group_value('server')
            serverNums = len(serverList)
            kpiSerObj = None
            logging2.debug("serverList %s"%(serverList))
            for i in range( 0, serverNums ):
                #创建监听线程，每个端口一个线程
                kpiSerObj = None
                kpiSerObj = DCKpiAggService(self.m_isRun, serverList[i], self.m_counter, self.m_cfgPidNums, self.m_parentPipe)
                if kpiSerObj.bindSocket() < 0:
                    logging2.error("create socket failed!")
                    return -1
                kpiSerObj.start()
                self.m_listenThreads.append(kpiSerObj)
            return 0    
        except Exception as err:
            logging2.error("create socket failed!")
            traceback.print_exc()
            logging2.error(str(err))
            return -1

    def exitDeal(self):
        try:
            for i in range( 0, len(self.m_listenThreads) ):
                self.m_listenThreads[i].setRunState(False)
                self.m_listenThreads[i].join()

            logging2.warn("listenThread exit!")

            for i in range( 0, self.m_cfgPidNums ):
                self.m_parentPipe[i].send((False,False))
                self.m_parentPipe[i].close()
                self.m_childConn[i].close()
                self.m_childPid[i].join()
            return 0
        except Exception as err:
            stack_trace = traceback.format_exc()
            logging2.error(stack_trace)
            traceback.print_exc()
            logging2.error(str(err))
            return -1

    def run(self):
        while True:
            if not self.m_isRun:
                logging2.warn("DCKpiAggServMan thread exit!")
                break
            try:
                for i in range( 0, self.m_cfgPidNums ):
                    if not self.m_childPid[i].is_alive():
                        current_time = time.time()
                        if current_time - self.last_restart_time[i] < 60:
                            self.restart_counts[i] += 1
                        else:
                            self.restart_counts[i] = 1
                        self.last_restart_time[i] = current_time
                        if self.restart_counts[i] > 3:
                            logging2.error(f"Process {i} has restarted more than 3 times in 1 minute. Exiting.")
                            sys.exit(-1)  # 直接退出主进程  
                        self.m_kpiConMgrObj[i] = DCKpiConMgr(self.m_cfgFile)
                        # self.m_childPid[i] = multiprocessing.Process(target=self.m_kpiConMgrObj[i].child_process, args=(self.m_childConn[i],))
                        self.m_childPid[i] = multiprocessing.Process(target=self.m_kpiConMgrObj[i].init_child_process, args=(self.m_childConn[i],))
                        self.m_childPid[i].start()
                time.sleep(5)
            except Exception as err:
                stack_trace = traceback.format_exc()
                logging2.error(stack_trace)
                self.exitDeal()
                traceback.print_exc()
                logging2.error(str(err))
                time.sleep(10)